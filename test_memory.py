#!/usr/bin/env python3
"""
Test script for the Advanced Memory System
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from codev2 import AdvancedMemorySystem, process_memory_tokens

def test_memory_system():
    """Test the memory system functionality"""
    print("Testing Advanced Memory System...")
    
    # Initialize memory system
    memory = AdvancedMemorySystem("test_memory.db")
    
    # Test storing memories
    print("\n1. Testing memory storage...")
    memory_id = memory.store_memory(
        memory_type="user_preference",
        content="User loves pizza",
        context="Mentioned during casual conversation",
        user_id=12345,
        channel_id=67890,
        guild_id=11111,
        importance=7,
        tags=["food", "preference"]
    )
    print(f"Stored memory with ID: {memory_id}")
    
    # Test retrieving memories
    print("\n2. Testing memory retrieval...")
    memories = memory.retrieve_memories(user_id=12345, limit=5)
    print(f"Retrieved {len(memories)} memories:")
    for mem in memories:
        print(f"  - [{mem['memory_type']}] {mem['content']}")
    
    # Test user profile
    print("\n3. Testing user profile...")
    memory.update_user_profile(
        user_id=12345,
        username="TestUser",
        display_name="Test User",
        personality_notes="Friendly and curious",
        preferences={"favorite_color": "blue", "hobby": "gaming"}
    )
    
    profile = memory.get_user_profile(12345)
    print(f"User profile: {profile}")
    
    # Test search
    print("\n4. Testing memory search...")
    search_results = memory.search_memories("pizza", limit=3)
    print(f"Search results for 'pizza': {len(search_results)} found")
    for result in search_results:
        print(f"  - {result['content']}")
    
    # Test knowledge base
    print("\n5. Testing knowledge base...")
    memory.add_knowledge(
        topic="Discord Bots",
        information="Discord bots can use slash commands for better user interaction",
        source="Development experience",
        confidence=0.9
    )
    
    knowledge = memory.get_knowledge("Discord", limit=3)
    print(f"Knowledge about Discord: {len(knowledge)} entries")
    for k in knowledge:
        print(f"  - {k['topic']}: {k['information']}")
    
    # Test memory tokens
    print("\n6. Testing memory tokens...")
    test_response = "I love chocolate! [REMEMBER:user_preference:8:User loves chocolate] That's interesting [UPDATE_PROFILE:personality_notes:Loves sweets]"
    
    cleaned_response = process_memory_tokens(
        test_response, 12345, 67890, 11111, "TestUser", "I love chocolate"
    )
    
    print(f"Original: {test_response}")
    print(f"Cleaned:  {cleaned_response}")
    
    # Check if memory was stored
    new_memories = memory.retrieve_memories(user_id=12345, limit=10)
    print(f"Total memories after token processing: {len(new_memories)}")
    
    # Test stats
    print("\n7. Testing memory statistics...")
    stats = memory.get_memory_stats()
    print(f"Memory stats: {stats}")
    
    print("\n✅ Memory system test completed successfully!")

if __name__ == "__main__":
    test_memory_system()
