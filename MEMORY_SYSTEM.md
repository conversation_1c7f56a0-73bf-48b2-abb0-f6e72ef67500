# Advanced Memory System for Discord Bot

## Overview

The Discord bot now includes a sophisticated memory system that allows the AI to remember users, their preferences, past conversations, and important information across bot restarts. The system uses SQLite for persistent storage and provides various memory types and operations.

## Features

### 🧠 Memory Types
- **User Preferences**: Personal likes, dislikes, and preferences
- **Facts**: General information and knowledge
- **Personal**: Personal information about users
- **Conversation**: Important conversation snippets
- **Custom**: Any other categorized memories

### 👤 User Profiles
- Username and display name tracking
- Personality notes and observations
- Relationship level (1-10 scale)
- Interaction count and history
- Custom preferences dictionary

### 🔍 Advanced Capabilities
- **Search**: Full-text search across all memories
- **Context-Aware**: Retrieves relevant memories for conversations
- **Importance Scoring**: Memories ranked by importance (1-10)
- **Automatic Cleanup**: Expired memories and optimization
- **Tag System**: Categorize memories with custom tags

## Memory Control Tokens

The AI can control memory operations using special tokens in responses. These tokens are processed and removed before sending messages to Discord:

### Available Tokens

| Token | Purpose | Example |
|-------|---------|---------|
| `[REMEMBER:type:importance:content]` | Store a memory | `[REMEMBER:user_preference:7:User loves pizza]` |
| `[UPDATE_PROFILE:field:value]` | Update user profile | `[UPDATE_PROFILE:personality_notes:Very friendly]` |
| `[STORE_FACT:topic:content]` | Store knowledge | `[STORE_FACT:cats:Cats sleep 16 hours per day]` |
| `[IGNORE]` | Don't respond | `[IGNORE]` |

### Profile Fields
- `personality_notes`: General personality observations
- `relationship_level`: Relationship strength (1-10)
- `pref_[key]`: Store preferences (e.g., `pref_favorite_color:blue`)

## Slash Commands

### User Commands
- `/my_profile` - View your profile and memories
- `/memory` - View memory system statistics
- `/forget_me` - Clear your data (with confirmation)

### Bot Management
- `/status` - Bot status including memory stats
- `/help` - Complete help including memory features

## Database Schema

### Tables
1. **memories** - Main memory storage
2. **user_profiles** - User information and preferences  
3. **conversation_summaries** - Conversation history summaries
4. **knowledge_base** - Facts and general knowledge

### Key Features
- Automatic indexing for performance
- Configurable memory limits per type
- Expiration system for temporary memories
- Access tracking and statistics

## Configuration

```python
# Memory system settings in codev2.py
MEMORY_DB_PATH = "bot_memory.db"
MAX_MEMORIES_PER_TYPE = 1000
MEMORY_CLEANUP_INTERVAL = 3600  # 1 hour
```

## Usage Examples

### For Users
1. **Share preferences**: "I love pizza" → Bot remembers this
2. **Personal info**: "My name is John" → Stored in profile
3. **Facts**: "Did you know cats sleep 16 hours?" → Added to knowledge base

### For Developers
```python
# Store a memory
memory_system.store_memory(
    memory_type="user_preference",
    content="User loves coffee",
    user_id=123456,
    importance=6
)

# Retrieve memories
memories = memory_system.retrieve_memories(user_id=123456, limit=5)

# Search memories
results = memory_system.search_memories("coffee")
```

## Testing

Run the test script to verify functionality:
```bash
python test_memory.py
```

## Benefits

1. **Personalized Interactions**: Bot remembers user preferences and history
2. **Persistent Knowledge**: Information survives bot restarts
3. **Context Awareness**: Relevant memories enhance conversations
4. **Privacy Control**: Users can view and delete their data
5. **Scalable**: Handles multiple servers and thousands of users
6. **Automatic**: AI manages memory operations transparently

## Privacy & Data Management

- Users can view their profile with `/my_profile`
- Users can delete their data with `/forget_me`
- Memory cleanup prevents unlimited growth
- Local SQLite database (no external services)
- Configurable retention policies

The memory system transforms the bot from a stateless responder into an intelligent assistant that learns and remembers, providing increasingly personalized and contextual interactions over time.
