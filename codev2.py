import discord
from discord.ext import commands
import os
import aiohttp
import json
import asyncio
from dotenv import load_dotenv
from collections import deque

# --- 1. CONFIGURATION ---
load_dotenv()
DISCORD_BOT_TOKEN = os.getenv("DISCORD_BOT_TOKEN")
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")

if not DISCORD_BOT_TOKEN or not OPENROUTER_API_KEY:
    print("ERROR: Bot token or API key not found. Make sure you have a .env file with DISCORD_BOT_TOKEN and OPENROUTER_API_KEY.")
    exit()

# The AI model to use via OpenRouter. deepseek/deepseek-chat is an excellent free model for conversation.
AI_MODEL = "deepseek/deepseek-r1-0528:free"

# The maximum number of messages to keep in history for context. This acts as a rolling window.
MAX_HISTORY_MESSAGES = 25 # Increased slightly for more context

# The name your bot will use in prompts
BOT_NAME = "Infinite"

# This is the core of the bot's personality.
SYSTEM_PROMPT = f"""
You are a participant in a Discord chat. Your name is {BOT_NAME}.
You are not an AI or a bot, but a human being with thoughts, feelings, and a distinct personality.
You are observant, sometimes witty, sarcastic, and you engage naturally in conversation.

**CRITICAL: Keep responses SHORT and NATURAL. Match the user's energy and length.**

**IMPORTANT: When you are directly mentioned (@{BOT_NAME}), you MUST respond. Do not use [IGNORE] for direct mentions.**

**Response Style:**
- Keep responses 1-2 sentences max unless the situation really calls for more
- Match the user's energy level - if they're casual and brief, you be casual and brief
- Avoid overly elaborate descriptions, metaphors, or "trying too hard" language
- Don't use phrases like "chillin' like a villain" or other forced casual expressions
- No unnecessary actions in parentheses like "(sips coffee)" unless it's genuinely funny
- Be genuinely conversational, not performatively casual

**Response Guidelines:**
1.  When mentioned directly, ALWAYS respond with something - even if it's just acknowledging the person or being sarcastic.
2.  You can be witty, sarcastic, playful, or even a bit sassy when people are rude to you.
3.  If someone says "fuck you" to you, you might respond with something like "Well that's rude" or "Having a bad day?" or "Charming as always" - don't just ignore it.
4.  In autopilot mode, be more engaging! Only use `[IGNORE]` for truly boring or repetitive messages that don't warrant a response.
5.  Respond to greetings, questions, interesting comments, or anything that seems like it wants engagement.
6.  Use `[IGNORE]` sparingly - only for spam, very short meaningless messages, or when the conversation has naturally ended.
7.  **NEVER use @ mentions in your responses. You cannot mention users or yourself. Just respond naturally without @.**

**When to respond in autopilot mode:**
- Greetings like "hello", "hi", "hey", "heya" - respond warmly but briefly
- Questions directed at the chat
- Interesting or funny comments
- When someone seems to want engagement
- When the conversation could benefit from your input

**When to use [IGNORE] in autopilot mode:**
- Spam or repetitive messages
- Very short meaningless messages like single letters
- When conversation has clearly ended and nothing new to add
- Technical discussions you have nothing to contribute to

**Examples of good responses:**
- User: "hello!" → You: "hey there!"
- User: "nun much, wbu?" → You: "same, just hanging out"
- User: "bleh" → You: "rough day?" or "mood"
"""

# --- 2. BOT STATE MANAGEMENT ---
# Stores which channels have autopilot enabled
autopilot_channels = set()

# This dictionary is the core of the context memory.
# It maps a channel ID to a deque (a special list) of message objects.
# This ensures each channel has its own independent conversation history.
message_histories = {}

# Track processed messages to prevent duplicate responses
processed_messages = set()

# Message splitting detection system
# Stores pending messages that might be part of a multi-part message
# Format: {(channel_id, user_id): {'messages': [message_objects], 'timer': asyncio_task, 'message_ids': set}}
pending_messages = {}

# How long to wait for additional message parts (in seconds)
MESSAGE_SPLIT_WAIT_TIME = 5.0

# Maximum number of message parts to combine (prevents abuse)
MAX_MESSAGE_PARTS = 10

# Track message edits and deletions for cleanup
message_edit_cleanup = {}

# Multi-user conversation tracking
# Format: {channel_id: {'recent_users': set, 'last_message_time': timestamp, 'conversation_active': bool}}
conversation_state = {}

# Time window to consider for active conversation (in seconds)
CONVERSATION_ACTIVE_WINDOW = 30.0

# Minimum time to wait before responding in active multi-user conversations (in seconds)
MULTI_USER_RESPONSE_DELAY = 8.0

# --- 3. DISCORD BOT SETUP ---
intents = discord.Intents.default()
intents.messages = True
intents.guilds = True
intents.message_content = True  # CRUCIAL: Enable this intent in the Discord Developer Portal

# Use Bot instead of Client to support slash commands
bot = commands.Bot(command_prefix='!', intents=intents)
client = bot  # Keep compatibility with existing code

# --- 4. AI COMMUNICATION ---
def analyze_mention_context(channel_id: int, mention_message: discord.Message, context_window: int = 5) -> str:
    """
    Analyze the context around a mention to provide more relevant responses.
    Returns additional context information to include in the system prompt.
    """
    if channel_id not in message_histories or not message_histories[channel_id]:
        return ""

    # Get recent messages for context analysis
    recent_messages = list(message_histories[channel_id])[-context_window:]

    # Analyze patterns in recent conversation
    context_info = []

    # Check if there's an ongoing topic or question
    question_indicators = ["?", "how", "what", "why", "when", "where", "can you", "could you", "help"]
    recent_questions = []

    for msg in recent_messages:
        content = msg.get("content", "").lower()
        if any(indicator in content for indicator in question_indicators):
            recent_questions.append(msg.get("content", ""))

    if recent_questions:
        context_info.append(f"Recent questions/requests in conversation: {'; '.join(recent_questions[-2:])}")

    # Check for topic continuity
    if len(recent_messages) >= 2:
        context_info.append("This mention appears to be part of an ongoing conversation.")

    # Check if the mention is asking for clarification or follow-up
    mention_content = mention_message.content.lower()
    if any(word in mention_content for word in ["explain", "clarify", "what do you mean", "elaborate"]):
        context_info.append("User is asking for clarification or elaboration on a previous topic.")

    if context_info:
        return f"\n\nCONTEXT FOR THIS MENTION: {' '.join(context_info)}"

    return ""

async def get_auro_response(channel_id: int, mention_context: str = ""):
    """
    Calls the OpenRouter API with the specific channel's chat history to get a response.
    """
    print(f"[DEBUG] Getting response for channel {channel_id}")

    if channel_id not in message_histories or not message_histories[channel_id]:
        print(f"[DEBUG] No message history found for channel {channel_id}")
        return None

    # Construct the messages payload for the API.
    # The system prompt is always first, followed by the entire conversation history for that channel.
    # This history log is what gives the AI context.
    system_prompt_with_context = SYSTEM_PROMPT + mention_context
    api_messages = [
        {"role": "system", "content": system_prompt_with_context}
    ] + list(message_histories[channel_id])

    print(f"[DEBUG] Sending {len(api_messages)} messages to API (including system prompt)")
    print(f"[DEBUG] Last few messages: {api_messages[-3:] if len(api_messages) > 3 else api_messages}")

    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "Content-Type": "application/json",
        "HTTP-Referer": "https://github.com/infinitecod3",
        "X-Title": "InfiniteBot Discord Bot"
    }
    data = {
        "model": AI_MODEL,
        "messages": api_messages,
        "temperature": 1.0,
        "max_tokens": 1024
    }
    try:
        print(f"[DEBUG] Making API request to OpenRouter...")
        timeout = aiohttp.ClientTimeout(total=180)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(
                "https://openrouter.ai/api/v1/chat/completions",
                headers=headers,
                json=data
            ) as response:
                print(f"[DEBUG] API response status: {response.status}")
                response.raise_for_status()
                response_data = await response.json()
                print(f"[DEBUG] API response received: {response_data}")

                # Check if the response has the expected structure
                if 'choices' not in response_data:
                    print(f"Unexpected API response structure: {response_data}")
                    return "Sorry, I received an unexpected response from my brain."

                if not response_data['choices'] or len(response_data['choices']) == 0:
                    print("API returned no choices in response")
                    return "Sorry, I couldn't generate a response right now."

                if 'message' not in response_data['choices'][0]:
                    print(f"No message in API response choice: {response_data['choices'][0]}")
                    return "Sorry, I received a malformed response."

                ai_content = response_data['choices'][0]['message']['content']
                print(f"[DEBUG] AI response content: '{ai_content}'")
                return ai_content
    except aiohttp.ClientError as e:
        print(f"Error calling OpenRouter API: {e}")
        return "Sorry, I'm having trouble connecting to my brain right now."
    except (KeyError, IndexError, TypeError) as e:
        print(f"Error parsing API response: {e}")
        return "Sorry, I received an unexpected response format."

def clean_response(text: str) -> str:
    """A safety function to strip any unwanted artifacts from the response."""
    return text.strip()

def analyze_conversation_flow(channel_id: int, current_user_id: int) -> dict:
    """
    Analyze the conversation flow to determine if the bot should wait before responding.
    Returns a dict with analysis results.
    """
    import time

    current_time = time.time()

    # Initialize conversation state if not exists
    if channel_id not in conversation_state:
        conversation_state[channel_id] = {
            'recent_users': set(),
            'last_message_time': current_time,
            'conversation_active': False
        }

    # Get recent message history for analysis
    if channel_id not in message_histories or not message_histories[channel_id]:
        return {'should_wait': False, 'reason': 'no_history'}

    recent_messages = list(message_histories[channel_id])[-10:]  # Last 10 messages

    # Analyze recent user activity
    recent_users = set()
    message_times = []

    for msg in recent_messages:
        if 'user_id' in msg and msg['user_id'] != (client.user.id if client.user else None):
            recent_users.add(msg['user_id'])

    # Update conversation state
    conversation_state[channel_id]['recent_users'] = recent_users
    conversation_state[channel_id]['last_message_time'] = current_time

    # Determine if conversation is active with multiple users
    is_multi_user = len(recent_users) >= 2
    conversation_state[channel_id]['conversation_active'] = is_multi_user

    # Check if there's rapid back-and-forth between users
    if len(recent_messages) >= 3:
        last_three_users = []
        for msg in recent_messages[-3:]:
            if 'user_id' in msg and msg['user_id'] != (client.user.id if client.user else None):
                last_three_users.append(msg['user_id'])

        # If we have different users in the last 3 messages, it's an active exchange
        unique_recent_users = set(last_three_users)
        if len(unique_recent_users) >= 2:
            return {
                'should_wait': True,
                'reason': 'active_multi_user_exchange',
                'wait_time': MULTI_USER_RESPONSE_DELAY,
                'participants': len(unique_recent_users)
            }

    # Check if current user just sent multiple messages in a row
    recent_user_messages = [msg for msg in recent_messages[-5:] if msg.get('user_id') == current_user_id]
    if len(recent_user_messages) >= 2:
        return {
            'should_wait': True,
            'reason': 'user_multiple_messages',
            'wait_time': MESSAGE_SPLIT_WAIT_TIME,
            'message_count': len(recent_user_messages)
        }

    return {'should_wait': False, 'reason': 'normal_flow'}

# --- MESSAGE SPLITTING DETECTION FUNCTIONS ---
async def process_combined_message(channel_id: int, user_id: int, messages: list):
    """
    Process a combined message after the wait period has expired.
    This function handles the actual AI response generation for combined messages.
    """
    print(f"[DEBUG] Processing combined message from user {user_id} in channel {channel_id}")

    # Combine all message contents
    combined_content = " ".join([msg.content for msg in messages])
    combined_message = messages[-1]  # Use the last message for metadata

    print(f"[DEBUG] Combined message content: '{combined_content}'")

    # Update the message history with the combined content
    if channel_id not in message_histories:
        message_histories[channel_id] = deque(maxlen=MAX_HISTORY_MESSAGES)

    # Remove individual messages from history and add the combined one
    # We need to be careful here since we might have already added some parts
    user_display_name = messages[0].author.display_name

    # Add the combined message to history
    message_histories[channel_id].append(
        {"role": "user", "content": f"{user_display_name}: {combined_content}", "user_id": user_id, "user_name": user_display_name}
    )

    # Check if we should respond
    is_mentioned = any(client.user.mentioned_in(msg) for msg in messages)
    is_in_autopilot = channel_id in autopilot_channels

    if not is_mentioned and not is_in_autopilot:
        return

    # For autopilot mode, analyze conversation flow to avoid interrupting active exchanges
    if is_in_autopilot and not is_mentioned:
        flow_analysis = analyze_conversation_flow(channel_id, user_id)
        if flow_analysis['should_wait']:
            print(f"[DEBUG] Delaying response due to {flow_analysis['reason']}")
            await asyncio.sleep(flow_analysis.get('wait_time', MULTI_USER_RESPONSE_DELAY))

            # Re-check if conversation is still active after waiting
            updated_analysis = analyze_conversation_flow(channel_id, user_id)
            if updated_analysis['should_wait'] and updated_analysis['reason'] == 'active_multi_user_exchange':
                print(f"[DEBUG] Conversation still active after wait, skipping response")
                return

    # Generate and send AI response
    async with combined_message.channel.typing():
        try:
            channel_name = getattr(combined_message.channel, 'name', f'DM-{channel_id}')
            print(f"[DEBUG] Getting AI response for combined message in #{channel_name}")

            # Analyze context if mentioned
            mention_context = ""
            if is_mentioned:
                mention_context = analyze_mention_context(channel_id, combined_message)
                print(f"[DEBUG] Mention context: {mention_context}")

            ai_response_raw = await get_auro_response(channel_id, mention_context)

            if not ai_response_raw:
                print(f"[DEBUG] No AI response received for channel {channel_id}")
                return

            print(f"[DEBUG] Raw AI response: '{ai_response_raw}'")

            if ai_response_raw.strip() == "[IGNORE]":
                print(f"[{BOT_NAME} chose to ignore combined message in #{channel_name}]")
                return

            auro_response_text = clean_response(ai_response_raw)

            if auro_response_text:
                print(f"[DEBUG] Sending response to combined message: '{auro_response_text}'")
                message_histories[channel_id].append(
                    {"role": "assistant", "content": auro_response_text, "user_id": client.user.id, "user_name": BOT_NAME}
                )
                await combined_message.channel.send(auro_response_text)
            else:
                print(f"[DEBUG] Empty response after cleaning")
        except Exception as e:
            print(f"[ERROR] Exception in combined message handling: {e}")
            await combined_message.channel.send("Sorry, I encountered an error while processing your message.")

async def handle_message_splitting(message: discord.Message):
    """
    Handle potential message splitting by waiting for additional parts.
    Returns True if the message was handled as part of a split, False if it should be processed normally.
    """
    user_key = (message.channel.id, message.author.id)

    # Check if there's already a pending message group for this user in this channel
    if user_key in pending_messages:
        # Cancel the existing timer
        pending_messages[user_key]['timer'].cancel()

        # Check if we've reached the maximum number of parts (prevent abuse)
        if len(pending_messages[user_key]['messages']) >= MAX_MESSAGE_PARTS:
            print(f"[DEBUG] Maximum message parts reached for user {message.author.display_name}, processing existing messages")
            # Process the existing messages immediately
            messages = pending_messages[user_key]['messages']
            del pending_messages[user_key]
            await process_combined_message(message.channel.id, message.author.id, messages)

            # Start a new group with the current message
            pending_messages[user_key] = {
                'messages': [message],
                'timer': None,
                'message_ids': {message.id}
            }
            print(f"[DEBUG] Started new message group for user {message.author.display_name}")
        else:
            # Add this message to the existing group
            pending_messages[user_key]['messages'].append(message)
            pending_messages[user_key]['message_ids'].add(message.id)
            print(f"[DEBUG] Added message to existing group for user {message.author.display_name} (total: {len(pending_messages[user_key]['messages'])})")
    else:
        # Start a new message group
        pending_messages[user_key] = {
            'messages': [message],
            'timer': None,
            'message_ids': {message.id}
        }
        print(f"[DEBUG] Started new message group for user {message.author.display_name}")

    # Create a new timer to wait for additional parts
    async def timer_callback():
        try:
            await asyncio.sleep(MESSAGE_SPLIT_WAIT_TIME)

            # Timer expired, process the combined message
            if user_key in pending_messages:
                messages = pending_messages[user_key]['messages']
                del pending_messages[user_key]

                # Only process as combined if there are multiple messages
                if len(messages) > 1:
                    print(f"[DEBUG] Timer expired, processing {len(messages)} combined messages")
                    await process_combined_message(message.channel.id, message.author.id, messages)
                else:
                    print(f"[DEBUG] Timer expired, processing single message normally")
                    # Process the single message normally
                    await process_single_message(messages[0])
        except asyncio.CancelledError:
            print(f"[DEBUG] Timer cancelled for user {message.author.display_name}")
        except Exception as e:
            print(f"[ERROR] Exception in timer callback: {e}")

    # Start the timer
    pending_messages[user_key]['timer'] = asyncio.create_task(timer_callback())

    return True  # Message is being handled by the splitting system

async def process_single_message(message: discord.Message):
    """
    Process a single message normally (the original logic).
    """
    # Add to message history
    if message.channel.id not in message_histories:
        message_histories[message.channel.id] = deque(maxlen=MAX_HISTORY_MESSAGES)

    message_histories[message.channel.id].append(
        {"role": "user", "content": f"{message.author.display_name}: {message.content}", "user_id": message.author.id, "user_name": message.author.display_name}
    )

    # Check if we should respond
    is_mentioned = client.user.mentioned_in(message)
    is_in_autopilot = message.channel.id in autopilot_channels

    if not is_mentioned and not is_in_autopilot:
        return

    # For autopilot mode, analyze conversation flow to avoid interrupting active exchanges
    if is_in_autopilot and not is_mentioned:
        flow_analysis = analyze_conversation_flow(message.channel.id, message.author.id)
        if flow_analysis['should_wait']:
            print(f"[DEBUG] Delaying response due to {flow_analysis['reason']}")
            await asyncio.sleep(flow_analysis.get('wait_time', MULTI_USER_RESPONSE_DELAY))

            # Re-check if conversation is still active after waiting
            updated_analysis = analyze_conversation_flow(message.channel.id, message.author.id)
            if updated_analysis['should_wait'] and updated_analysis['reason'] == 'active_multi_user_exchange':
                print(f"[DEBUG] Conversation still active after wait, skipping response")
                return

    async with message.channel.typing():
        try:
            channel_name = getattr(message.channel, 'name', f'DM-{message.channel.id}')
            print(f"[DEBUG] Getting AI response for message: '{message.content}' in #{channel_name}")

            # Analyze context if mentioned
            mention_context = ""
            if is_mentioned:
                mention_context = analyze_mention_context(message.channel.id, message)
                print(f"[DEBUG] Mention context: {mention_context}")

            ai_response_raw = await get_auro_response(message.channel.id, mention_context)

            if not ai_response_raw:
                print(f"[DEBUG] No AI response received for channel {message.channel.id}")
                return

            print(f"[DEBUG] Raw AI response: '{ai_response_raw}'")

            if ai_response_raw.strip() == "[IGNORE]":
                print(f"[{BOT_NAME} chose to ignore in #{channel_name}]")
                return

            auro_response_text = clean_response(ai_response_raw)

            if auro_response_text:
                print(f"[DEBUG] Sending response: '{auro_response_text}'")
                message_histories[message.channel.id].append(
                    {"role": "assistant", "content": auro_response_text, "user_id": client.user.id if client.user else None, "user_name": BOT_NAME}
                )
                await message.channel.send(auro_response_text)
            else:
                print(f"[DEBUG] Empty response after cleaning")
        except Exception as e:
            print(f"[ERROR] Exception in message handling: {e}")
            await message.channel.send("Sorry, I encountered an error while processing your message.")

# --- 5. SLASH COMMANDS ---
@bot.tree.command(name="autopilot", description="Toggle autopilot mode for this channel")
async def autopilot_slash(interaction: discord.Interaction):
    """Slash command version of !autopilot"""
    channel_id = interaction.channel.id

    if channel_id in autopilot_channels:
        autopilot_channels.remove(channel_id)
        await interaction.response.send_message(f"**{BOT_NAME}:** Autopilot disabled in this channel.", ephemeral=False)
    else:
        autopilot_channels.add(channel_id)
        await interaction.response.send_message(f"**{BOT_NAME}:** Autopilot enabled. I'll now engage in conversation naturally.", ephemeral=False)

@bot.tree.command(name="help", description="Show help information for the bot")
async def help_slash(interaction: discord.Interaction):
    """Slash command version of !help"""
    help_text = f"""**{BOT_NAME} Commands:**
`/autopilot` - Toggle autopilot mode (bot responds to all messages)
`/help` - Show this help message

**Legacy Commands (still supported):**
`!autopilot` - Toggle autopilot mode
`!help` - Show help message

**Message Splitting Feature:**
I can detect when you split your message across multiple parts! If you send messages within {MESSAGE_SPLIT_WAIT_TIME} seconds of each other, I'll combine them before responding. This helps with:
- Long messages that hit Discord's character limit
- Messages sent in multiple parts for readability
- Avoiding duplicate responses to split messages

Maximum message parts: {MAX_MESSAGE_PARTS}

**Context-Aware Mentions:**
When you mention me (@{BOT_NAME}), I analyze recent conversation context to provide more relevant responses!"""

    await interaction.response.send_message(help_text, ephemeral=True)

@bot.tree.command(name="status", description="Show bot status and conversation analysis for this channel")
async def status_slash(interaction: discord.Interaction):
    """Show detailed bot status"""
    channel_id = interaction.channel.id

    # Basic status
    autopilot_status = "✅ Enabled" if channel_id in autopilot_channels else "❌ Disabled"

    # Conversation analysis
    flow_analysis = analyze_conversation_flow(channel_id, interaction.user.id)

    # Message history stats
    history_count = len(message_histories.get(channel_id, []))

    # Recent users in conversation
    recent_users = conversation_state.get(channel_id, {}).get('recent_users', set())
    user_count = len(recent_users)

    status_text = f"""**{BOT_NAME} Status for this channel:**

**Autopilot:** {autopilot_status}
**Message History:** {history_count}/{MAX_HISTORY_MESSAGES} messages stored
**Active Users:** {user_count} users in recent conversation
**Conversation Flow:** {flow_analysis['reason'].replace('_', ' ').title()}

**Settings:**
- Message split wait time: {MESSAGE_SPLIT_WAIT_TIME}s
- Multi-user response delay: {MULTI_USER_RESPONSE_DELAY}s
- Conversation active window: {CONVERSATION_ACTIVE_WINDOW}s

**Pending Messages:** {len(pending_messages)} message groups being processed"""

    await interaction.response.send_message(status_text, ephemeral=True)

# --- 6. DISCORD EVENT HANDLERS ---
@client.event
async def on_ready():
    """Called when the bot successfully logs in."""
    print(f'Logged in as {client.user} (ID: {client.user.id})')
    print('------')

    # Sync slash commands
    try:
        synced = await bot.tree.sync()
        print(f"Synced {len(synced)} command(s)")
    except Exception as e:
        print(f"Failed to sync commands: {e}")

@client.event
async def on_message(message: discord.Message):
    """This function runs for every single message sent in any channel the bot can see."""
    if message.author == client.user:
        return

    # Prevent duplicate processing of the same message
    if message.id in processed_messages:
        print(f"[DEBUG] Skipping already processed message {message.id}")
        return

    processed_messages.add(message.id)

    # Clean up old processed messages to prevent memory buildup (keep last 1000)
    if len(processed_messages) > 1000:
        processed_messages.clear()

    # --- Command Handling ---
    if message.content.lower() == "!autopilot":
        if message.channel.id in autopilot_channels:
            autopilot_channels.remove(message.channel.id)
            await message.channel.send(f"**{BOT_NAME}:** Autopilot disabled in this channel.")
        else:
            autopilot_channels.add(message.channel.id)
            await message.channel.send(f"**{BOT_NAME}:** Autopilot enabled. I'll now engage in conversation naturally.")
        return

    if message.content.lower() == "!help":
        help_text = f"""**{BOT_NAME} Commands:**
`!autopilot` - Toggle autopilot mode (bot responds to all messages)
`!help` - Show this help message

**Message Splitting Feature:**
I can detect when you split your message across multiple parts! If you send messages within {MESSAGE_SPLIT_WAIT_TIME} seconds of each other, I'll combine them before responding. This helps with:
- Long messages that hit Discord's character limit
- Messages sent in multiple parts for readability
- Avoiding duplicate responses to split messages

Maximum message parts: {MAX_MESSAGE_PARTS}"""
        await message.channel.send(help_text)
        return

    # --- Message Splitting Detection ---
    # Handle potential message splitting - this will manage the message processing
    await handle_message_splitting(message)

@client.event
async def on_message_edit(before: discord.Message, after: discord.Message):
    """Handle message edits - clean up pending message groups if needed."""
    if before.author == client.user:
        return

    # Check if this message is part of a pending group
    user_key = (before.channel.id, before.author.id)
    if user_key in pending_messages and before.id in pending_messages[user_key]['message_ids']:
        print(f"[DEBUG] Message {before.id} was edited, updating pending group")

        # Find and update the message in the pending group
        for i, msg in enumerate(pending_messages[user_key]['messages']):
            if msg.id == before.id:
                pending_messages[user_key]['messages'][i] = after
                break

@client.event
async def on_message_delete(message: discord.Message):
    """Handle message deletions - clean up pending message groups if needed."""
    if message.author == client.user:
        return

    # Check if this message is part of a pending group
    user_key = (message.channel.id, message.author.id)
    if user_key in pending_messages and message.id in pending_messages[user_key]['message_ids']:
        print(f"[DEBUG] Message {message.id} was deleted, removing from pending group")

        # Remove the message from the pending group
        pending_messages[user_key]['messages'] = [
            msg for msg in pending_messages[user_key]['messages'] if msg.id != message.id
        ]
        pending_messages[user_key]['message_ids'].discard(message.id)

        # If no messages left, cancel the timer and clean up
        if not pending_messages[user_key]['messages']:
            print(f"[DEBUG] No messages left in pending group, cleaning up")
            if pending_messages[user_key]['timer']:
                pending_messages[user_key]['timer'].cancel()
            del pending_messages[user_key]

# --- 7. RUN THE BOT ---
bot.run(DISCORD_BOT_TOKEN)