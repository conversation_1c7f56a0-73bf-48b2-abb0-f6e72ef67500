#!/usr/bin/env python3
"""
Test script to verify the message splitting functionality works correctly.
This script simulates the message splitting behavior without running the full Discord bot.
"""

import asyncio
from collections import deque
from unittest.mock import Mock, AsyncMock

# Mock Discord objects for testing
class MockMessage:
    def __init__(self, content, author_name, channel_id, user_id, message_id):
        self.content = content
        self.author = Mock()
        self.author.display_name = author_name
        self.author.id = user_id
        self.channel = Mock()
        self.channel.id = channel_id
        self.channel.send = AsyncMock()
        self.channel.typing = AsyncMock()
        self.id = message_id

class MockClient:
    def __init__(self):
        self.user = Mock()
        self.user.mentioned_in = Mock(return_value=False)

# Test configuration
MESSAGE_SPLIT_WAIT_TIME = 2.0  # Shorter for testing
MAX_HISTORY_MESSAGES = 25
pending_messages = {}
message_histories = {}
autopilot_channels = {12345}  # Test channel in autopilot mode
client = MockClient()

async def process_combined_message(channel_id: int, user_id: int, messages: list):
    """Mock version of process_combined_message for testing"""
    combined_content = " ".join([msg.content for msg in messages])
    print(f"✅ COMBINED MESSAGE: '{combined_content}' from user {user_id} in channel {channel_id}")
    print(f"   Total parts: {len(messages)}")
    return combined_content

async def process_single_message(message):
    """Mock version of process_single_message for testing"""
    print(f"✅ SINGLE MESSAGE: '{message.content}' from {message.author.display_name}")
    return message.content

async def handle_message_splitting(message):
    """
    Handle potential message splitting by waiting for additional parts.
    Returns True if the message was handled as part of a split, False if it should be processed normally.
    """
    user_key = (message.channel.id, message.author.id)
    
    # Check if there's already a pending message group for this user in this channel
    if user_key in pending_messages:
        # Cancel the existing timer
        pending_messages[user_key]['timer'].cancel()
        
        # Add this message to the existing group
        pending_messages[user_key]['messages'].append(message)
        print(f"[DEBUG] Added message to existing group for user {message.author.display_name} (total: {len(pending_messages[user_key]['messages'])})")
    else:
        # Start a new message group
        pending_messages[user_key] = {
            'messages': [message],
            'timer': None
        }
        print(f"[DEBUG] Started new message group for user {message.author.display_name}")
    
    # Create a new timer to wait for additional parts
    async def timer_callback():
        try:
            await asyncio.sleep(MESSAGE_SPLIT_WAIT_TIME)
            
            # Timer expired, process the combined message
            if user_key in pending_messages:
                messages = pending_messages[user_key]['messages']
                del pending_messages[user_key]
                
                # Only process as combined if there are multiple messages
                if len(messages) > 1:
                    print(f"[DEBUG] Timer expired, processing {len(messages)} combined messages")
                    await process_combined_message(message.channel.id, message.author.id, messages)
                else:
                    print(f"[DEBUG] Timer expired, processing single message normally")
                    # Process the single message normally
                    await process_single_message(messages[0])
        except asyncio.CancelledError:
            print(f"[DEBUG] Timer cancelled for user {message.author.display_name}")
        except Exception as e:
            print(f"[ERROR] Exception in timer callback: {e}")
    
    # Start the timer
    pending_messages[user_key]['timer'] = asyncio.create_task(timer_callback())
    
    return True  # Message is being handled by the splitting system

async def test_message_splitting():
    """Test the message splitting functionality"""
    print("🧪 Testing Message Splitting System")
    print("=" * 50)
    
    # Test 1: Single message (should be processed normally after timeout)
    print("\n📝 Test 1: Single message")
    msg1 = MockMessage("Hello there!", "TestUser", 12345, 1001, 1)
    await handle_message_splitting(msg1)
    await asyncio.sleep(MESSAGE_SPLIT_WAIT_TIME + 0.5)  # Wait for timer to expire
    
    # Test 2: Split message (should be combined)
    print("\n📝 Test 2: Split message")
    msg2a = MockMessage("This is the first part", "TestUser", 12345, 1001, 2)
    msg2b = MockMessage("and this is the second part", "TestUser", 12345, 1001, 3)
    
    await handle_message_splitting(msg2a)
    await asyncio.sleep(1.0)  # Wait 1 second
    await handle_message_splitting(msg2b)
    await asyncio.sleep(MESSAGE_SPLIT_WAIT_TIME + 0.5)  # Wait for timer to expire
    
    # Test 3: Three-part message
    print("\n📝 Test 3: Three-part message")
    msg3a = MockMessage("Part one", "TestUser", 12345, 1001, 4)
    msg3b = MockMessage("part two", "TestUser", 12345, 1001, 5)
    msg3c = MockMessage("and part three!", "TestUser", 12345, 1001, 6)
    
    await handle_message_splitting(msg3a)
    await asyncio.sleep(0.5)
    await handle_message_splitting(msg3b)
    await asyncio.sleep(0.5)
    await handle_message_splitting(msg3c)
    await asyncio.sleep(MESSAGE_SPLIT_WAIT_TIME + 0.5)
    
    # Test 4: Messages from different users (should be processed separately)
    print("\n📝 Test 4: Messages from different users")
    msg4a = MockMessage("User 1 message", "User1", 12345, 1001, 7)
    msg4b = MockMessage("User 2 message", "User2", 12345, 1002, 8)
    
    await handle_message_splitting(msg4a)
    await asyncio.sleep(0.5)
    await handle_message_splitting(msg4b)
    await asyncio.sleep(MESSAGE_SPLIT_WAIT_TIME + 0.5)
    
    print("\n✅ All tests completed!")

if __name__ == "__main__":
    asyncio.run(test_message_splitting())
